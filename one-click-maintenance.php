<?php
/**
 * Plugin Name: One-Click Maintenance Mode
 * Plugin URI: https://github.com/yourusername/one-click-maintenance
 * Description: Easily activate a maintenance mode screen with one click, customizable content, and modern clean design.
 * Version: 1.2
 * Author: Amit
 * Author URI: https://yourwebsite.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: one-click-maintenance
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('OCM_PLUGIN_URL', plugin_dir_url(__FILE__));
define('OCM_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('OCM_PLUGIN_VERSION', '1.2');

/**
 * Main Plugin Class
 */
class OneClickMaintenance {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain('one-click-maintenance', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Initialize admin functionality
        if (is_admin()) {
            add_action('admin_menu', array($this, 'add_admin_menu'));
            add_action('admin_init', array($this, 'register_settings'));
            add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        }

        // Add AJAX handlers
        add_action('wp_ajax_ocm_disable_maintenance', array($this, 'ajax_disable_maintenance'));
        add_action('wp_ajax_nopriv_ocm_disable_maintenance', array($this, 'ajax_disable_maintenance'));
        add_action('wp_ajax_ocm_preview_maintenance', array($this, 'ajax_preview_maintenance'));
        add_action('wp_ajax_ocm_email_capture', array($this, 'ajax_email_capture'));
        add_action('wp_ajax_nopriv_ocm_email_capture', array($this, 'ajax_email_capture'));
        add_action('wp_ajax_ocm_export_settings', array($this, 'ajax_export_settings'));
        add_action('wp_ajax_ocm_import_settings', array($this, 'ajax_import_settings'));
        
        // Check if maintenance mode is enabled or preview mode
        if ($this->is_maintenance_active() || $this->is_preview_mode()) {
            add_action('template_redirect', array($this, 'show_maintenance_page'));
        }
        
        // Enqueue frontend styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Set default options
        $default_options = array(
            // General Settings
            'ocm_maintenance_enabled' => false,
            'ocm_bypass_roles' => array('administrator'),
            'ocm_send_503_header' => true,

            // Content Settings
            'ocm_title' => __('We\'re Under Maintenance', 'one-click-maintenance'),
            'ocm_description' => __('We are currently performing scheduled maintenance. We will be back online shortly.', 'one-click-maintenance'),
            'ocm_footer_message' => __('For urgent matters, please contact <NAME_EMAIL>', 'one-click-maintenance'),

            // Design Settings
            'ocm_font_family' => 'DM Sans',
            'ocm_font_size' => 'medium',
            'ocm_background_type' => 'color',
            'ocm_background_color' => '#f8f9fa',
            'ocm_background_image' => '',
            'ocm_logo' => '',
            'ocm_custom_css' => '',

            // Countdown Settings
            'ocm_countdown_enabled' => false,
            'ocm_countdown_date' => '',

            // Engagement & Branding
            'ocm_email_capture_enabled' => false,
            'ocm_social_links' => array(),
            'ocm_custom_favicon' => '',
            'ocm_meta_image' => '',

            // Advanced Settings
            'ocm_shortcode_support' => false,
            'ocm_multisite_mode' => false
        );
        
        foreach ($default_options as $option => $value) {
            if (get_option($option) === false) {
                add_option($option, $value);
            }
        }
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Disable maintenance mode on deactivation
        update_option('ocm_maintenance_enabled', false);
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_options_page(
            __('Maintenance Mode', 'one-click-maintenance'),
            __('Maintenance Mode', 'one-click-maintenance'),
            'manage_options',
            'one-click-maintenance',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        $settings = array(
            // General Settings
            'ocm_maintenance_enabled',
            'ocm_bypass_roles',
            'ocm_send_503_header',

            // Content Settings
            'ocm_title',
            'ocm_description',
            'ocm_footer_message',

            // Design Settings
            'ocm_font_family',
            'ocm_font_size',
            'ocm_background_type',
            'ocm_background_color',
            'ocm_background_image',
            'ocm_logo',
            'ocm_custom_css',

            // Countdown Settings
            'ocm_countdown_enabled',
            'ocm_countdown_date',

            // Engagement & Branding
            'ocm_email_capture_enabled',
            'ocm_social_links',
            'ocm_custom_favicon',
            'ocm_meta_image',

            // Advanced Settings
            'ocm_shortcode_support',
            'ocm_multisite_mode'
        );

        foreach ($settings as $setting) {
            register_setting('ocm_settings', $setting);
        }
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if ($hook !== 'settings_page_one-click-maintenance') {
            return;
        }
        
        wp_enqueue_media();
        wp_enqueue_script('wp-color-picker');
        wp_enqueue_style('wp-color-picker');
        
        wp_enqueue_script(
            'ocm-admin-js',
            OCM_PLUGIN_URL . 'js/admin.js',
            array('jquery', 'wp-color-picker'),
            OCM_PLUGIN_VERSION,
            true
        );
        
        wp_enqueue_style(
            'ocm-admin-css',
            OCM_PLUGIN_URL . 'css/admin.css',
            array(),
            OCM_PLUGIN_VERSION
        );

        // Localize script for AJAX
        wp_localize_script('ocm-admin-js', 'ocm_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ocm_admin_nonce'),
            'preview_nonce' => wp_create_nonce('ocm_preview_maintenance')
        ));
    }
    
    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_frontend_scripts() {
        if (get_option('ocm_maintenance_enabled', false) && !is_user_logged_in()) {
            wp_enqueue_style(
                'ocm-frontend-css',
                OCM_PLUGIN_URL . 'css/style.css',
                array(),
                OCM_PLUGIN_VERSION
            );
            
            // Enqueue Google Fonts
            $font_family = get_option('ocm_font_family', 'DM Sans');
            if ($font_family !== 'inherit') {
                $font_url = 'https://fonts.googleapis.com/css2?family=' . urlencode($font_family) . ':wght@300;400;500;600;700&display=swap';
                wp_enqueue_style('ocm-google-fonts', $font_url, array(), null);
            }
            
            // Enqueue countdown script if enabled
            if (get_option('ocm_countdown_enabled', false)) {
                wp_enqueue_script(
                    'ocm-countdown-js',
                    OCM_PLUGIN_URL . 'js/countdown.js',
                    array('jquery'),
                    OCM_PLUGIN_VERSION,
                    true
                );
                
                wp_localize_script('ocm-countdown-js', 'ocm_countdown', array(
                    'date' => get_option('ocm_countdown_date', ''),
                    'ajax_url' => admin_url('admin-ajax.php'),
                    'nonce' => wp_create_nonce('ocm_disable_maintenance'),
                    'labels' => array(
                        'days' => __('Days', 'one-click-maintenance'),
                        'hours' => __('Hours', 'one-click-maintenance'),
                        'minutes' => __('Minutes', 'one-click-maintenance'),
                        'seconds' => __('Seconds', 'one-click-maintenance')
                    )
                ));
            }

            // Enqueue email capture script if enabled
            if (get_option('ocm_email_capture_enabled', false)) {
                wp_enqueue_script(
                    'ocm-email-capture-js',
                    OCM_PLUGIN_URL . 'js/email-capture.js',
                    array('jquery'),
                    OCM_PLUGIN_VERSION,
                    true
                );

                wp_localize_script('ocm-email-capture-js', 'ocm_email', array(
                    'ajax_url' => admin_url('admin-ajax.php'),
                    'nonce' => wp_create_nonce('ocm_email_capture')
                ));
            }
        }
    }
    
    /**
     * Check if we're in preview mode
     */
    private function is_preview_mode() {
        return isset($_GET['ocm_preview']) &&
               isset($_GET['ocm_nonce']) &&
               wp_verify_nonce($_GET['ocm_nonce'], 'ocm_preview') &&
               current_user_can('manage_options');
    }

    /**
     * Show maintenance page
     */
    public function show_maintenance_page() {
        // In preview mode, always show maintenance page
        $is_preview = $this->is_preview_mode();

        // Check if current user can bypass maintenance mode (but not in preview)
        if (!$is_preview && $this->can_user_bypass_maintenance()) {
            return;
        }

        // Set 503 header for SEO if enabled (but not in preview mode)
        if (!$is_preview && get_option('ocm_send_503_header', true)) {
            status_header(503);
        }
        nocache_headers();

        // Whitelist login and admin screens to avoid lockout
        $request_uri = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';
        if (stripos($request_uri, 'wp-login.php') !== false || stripos($request_uri, 'wp-login') !== false || is_admin()) {
            return;
        }

        // Load maintenance page template
        include OCM_PLUGIN_PATH . 'templates/maintenance-page.php';
        exit;
    }

    /**
     * Check if current user can bypass maintenance mode
     */
    private function can_user_bypass_maintenance() {
        if (!is_user_logged_in()) {
            return false;
        }

        // Apply filter for custom bypass logic
        if (apply_filters('ocm_bypass_maintenance', false)) {
            return true;
        }

        $bypass_roles = get_option('ocm_bypass_roles', array('administrator'));
        if (!is_array($bypass_roles)) {
            $bypass_roles = array('administrator');
        }

        $current_user = wp_get_current_user();

        // Check multisite super admin
        if (is_multisite() && is_super_admin()) {
            return true;
        }

        foreach ($bypass_roles as $role) {
            if (in_array($role, $current_user->roles, true)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if maintenance mode should be active (considering multisite)
     */
    private function is_maintenance_active() {
        $maintenance_enabled = get_option('ocm_maintenance_enabled', false);

        // Check multisite mode
        if (is_multisite() && get_option('ocm_multisite_mode', false)) {
            // In multisite mode, check network-wide setting
            $network_maintenance = get_site_option('ocm_network_maintenance_enabled', false);
            return $network_maintenance || $maintenance_enabled;
        }

        return $maintenance_enabled;
    }
    
    /**
     * Admin page content
     */
    public function admin_page() {
        include OCM_PLUGIN_PATH . 'templates/admin-page.php';
    }

    /**
     * AJAX handler to disable maintenance mode when countdown ends
     */
    public function ajax_disable_maintenance() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'ocm_disable_maintenance')) {
            wp_send_json_error(__('Security check failed', 'one-click-maintenance'));
        }

        // Validate countdown has actually ended OR require admin capability
        $countdown_enabled = get_option('ocm_countdown_enabled', false);
        $countdown_date = get_option('ocm_countdown_date', '');
        $now_ts = current_time('timestamp');
        $end_ts = $countdown_date ? strtotime($countdown_date) : 0;

        $can_disable = false;
        if ($countdown_enabled && $end_ts && $now_ts >= $end_ts) {
            $can_disable = true;
        }

        // Allow admins to disable at any time
        if (current_user_can('manage_options')) {
            $can_disable = true;
        }

        if (!$can_disable) {
            wp_send_json_error(__('Maintenance cannot be disabled yet. Please wait until the countdown ends.', 'one-click-maintenance'));
        }

        // Disable maintenance mode
        update_option('ocm_maintenance_enabled', false);

        // Send success response
        wp_send_json_success(array(
            'message' => __('Maintenance mode disabled successfully', 'one-click-maintenance')
        ));
    }

    /**
     * AJAX handler for maintenance page preview
     */
    public function ajax_preview_maintenance() {
        try {
            // Verify nonce for security
            if (!wp_verify_nonce($_POST['nonce'], 'ocm_preview_maintenance')) {
                wp_send_json_error(__('Security check failed', 'one-click-maintenance'));
            }

            // Check user permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Insufficient permissions', 'one-click-maintenance'));
            }

            // Generate preview URL
            $preview_url = add_query_arg(array(
                'ocm_preview' => '1',
                'ocm_nonce' => wp_create_nonce('ocm_preview')
            ), home_url());

            // Send response
            wp_send_json_success(array(
                'preview_url' => $preview_url
            ));

        } catch (Exception $e) {
            wp_send_json_error(__('Error generating preview: ', 'one-click-maintenance') . $e->getMessage());
        }
    }

    /**
     * AJAX handler for email capture
     */
    public function ajax_email_capture() {
        try {
            // Verify nonce for security
            if (!wp_verify_nonce($_POST['nonce'], 'ocm_email_capture')) {
                wp_send_json_error(__('Security check failed', 'one-click-maintenance'));
            }

            // Check if email capture is enabled
            if (!get_option('ocm_email_capture_enabled', false)) {
                wp_send_json_error(__('Email capture is not enabled', 'one-click-maintenance'));
            }

            // Validate email
            $email = sanitize_email($_POST['email'] ?? '');
            if (empty($email) || !is_email($email)) {
                wp_send_json_error(__('Please enter a valid email address', 'one-click-maintenance'));
            }

            // Rate limiting - check if same IP submitted recently
            $recent_submissions = get_transient('ocm_email_submissions_' . md5($_SERVER['REMOTE_ADDR'] ?? ''));
            if ($recent_submissions && $recent_submissions >= 3) {
                wp_send_json_error(__('Too many submissions. Please try again later.', 'one-click-maintenance'));
            }

            // Get existing emails
            $emails = get_option('ocm_captured_emails', array());
            if (!is_array($emails)) {
                $emails = array();
            }

            // Check if email already exists
            if (in_array($email, $emails)) {
                wp_send_json_error(__('This email is already subscribed', 'one-click-maintenance'));
            }

            // Add email to list
            $emails[] = $email;
            $update_result = update_option('ocm_captured_emails', $emails);

            if (!$update_result) {
                wp_send_json_error(__('Failed to save email. Please try again.', 'one-click-maintenance'));
            }

            // Log the capture with timestamp
            $email_log = get_option('ocm_email_log', array());
            if (!is_array($email_log)) {
                $email_log = array();
            }

            $email_log[] = array(
                'email' => $email,
                'timestamp' => current_time('mysql'),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            );

            // Keep only last 1000 entries to prevent database bloat
            if (count($email_log) > 1000) {
                $email_log = array_slice($email_log, -1000);
            }

            update_option('ocm_email_log', $email_log);

            // Update rate limiting counter
            $current_count = $recent_submissions ? $recent_submissions : 0;
            set_transient('ocm_email_submissions_' . md5($_SERVER['REMOTE_ADDR'] ?? ''), $current_count + 1, 3600); // 1 hour

            // Send success response
            wp_send_json_success(__('Thank you! We\'ll notify you when we\'re back online.', 'one-click-maintenance'));

        } catch (Exception $e) {
            error_log('OCM Email Capture Error: ' . $e->getMessage());
            wp_send_json_error(__('An unexpected error occurred. Please try again.', 'one-click-maintenance'));
        }
    }

    /**
     * AJAX handler for exporting settings
     */
    public function ajax_export_settings() {
        try {
            // Verify nonce and permissions
            if (!wp_verify_nonce($_POST['nonce'], 'ocm_admin_nonce') || !current_user_can('manage_options')) {
                wp_send_json_error(__('Permission denied', 'one-click-maintenance'));
            }

            // Get all plugin settings
            $settings = array();
            $option_keys = array(
                'ocm_maintenance_enabled',
                'ocm_bypass_roles',
                'ocm_send_503_header',
                'ocm_title',
                'ocm_description',
                'ocm_footer_message',
                'ocm_font_family',
                'ocm_font_size',
                'ocm_background_type',
                'ocm_background_color',
                'ocm_background_image',
                'ocm_logo',
                'ocm_custom_css',
                'ocm_countdown_enabled',
                'ocm_countdown_date',
                'ocm_email_capture_enabled',
                'ocm_social_links',
                'ocm_custom_favicon',
                'ocm_meta_image',
                'ocm_shortcode_support',
                'ocm_multisite_mode'
            );

            foreach ($option_keys as $key) {
                $value = get_option($key, '');
                // Don't export sensitive data or large datasets
                if ($key === 'ocm_captured_emails' || $key === 'ocm_email_log') {
                    continue;
                }
                $settings[$key] = $value;
            }

            // Add metadata
            $export_data = array(
                'plugin' => 'one-click-maintenance',
                'version' => OCM_PLUGIN_VERSION,
                'export_date' => current_time('mysql'),
                'site_url' => home_url(),
                'wordpress_version' => get_bloginfo('version'),
                'settings' => $settings
            );

            wp_send_json_success($export_data);

        } catch (Exception $e) {
            error_log('OCM Export Error: ' . $e->getMessage());
            wp_send_json_error(__('Export failed: ', 'one-click-maintenance') . $e->getMessage());
        }
    }

    /**
     * AJAX handler for importing settings
     */
    public function ajax_import_settings() {
        try {
            // Verify nonce and permissions
            if (!wp_verify_nonce($_POST['nonce'], 'ocm_admin_nonce') || !current_user_can('manage_options')) {
                wp_send_json_error(__('Permission denied', 'one-click-maintenance'));
            }

            // Get and validate import data
            $raw_data = stripslashes($_POST['data'] ?? '');
            if (empty($raw_data)) {
                wp_send_json_error(__('No import data provided', 'one-click-maintenance'));
            }

            $import_data = json_decode($raw_data, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                wp_send_json_error(__('Invalid JSON format', 'one-click-maintenance'));
            }

            if (!$import_data || !isset($import_data['settings'])) {
                wp_send_json_error(__('Invalid import data structure', 'one-click-maintenance'));
            }

            if (!isset($import_data['plugin']) || $import_data['plugin'] !== 'one-click-maintenance') {
                wp_send_json_error(__('This file is not from One-Click Maintenance Mode plugin', 'one-click-maintenance'));
            }

            // Version compatibility check
            if (isset($import_data['version']) && version_compare($import_data['version'], '0.5.0', '<')) {
                wp_send_json_error(__('Import file is from an incompatible plugin version', 'one-click-maintenance'));
            }

            // Import settings with validation
            $imported_count = 0;
            $errors = array();

            foreach ($import_data['settings'] as $key => $value) {
                if (strpos($key, 'ocm_') === 0) {
                    try {
                        // Validate specific settings
                        if ($key === 'ocm_bypass_roles' && !is_array($value)) {
                            $value = array('administrator');
                        }
                        elseif ($key === 'ocm_social_links' && !is_array($value)) {
                            $value = array();
                        }
                        elseif (in_array($key, array('ocm_background_image', 'ocm_logo', 'ocm_custom_favicon', 'ocm_meta_image'))) {
                            $value = esc_url_raw($value);
                        }
                        elseif ($key === 'ocm_custom_css') {
                            $value = wp_strip_all_tags($value);
                        }

                        update_option($key, $value);
                        $imported_count++;

                    } catch (Exception $e) {
                        $errors[] = sprintf(__('Error importing %s: %s', 'one-click-maintenance'), $key, $e->getMessage());
                    }
                }
            }

            $response_data = array(
                'imported_count' => $imported_count
            );

            if (!empty($errors)) {
                $response_data['message'] = sprintf(__('Imported %d settings with %d errors', 'one-click-maintenance'), $imported_count, count($errors));
                $response_data['errors'] = $errors;
            } else {
                $response_data['message'] = sprintf(__('Successfully imported %d settings', 'one-click-maintenance'), $imported_count);
            }

            wp_send_json_success($response_data);

        } catch (Exception $e) {
            error_log('OCM Import Error: ' . $e->getMessage());
            wp_send_json_error(__('Import failed: ', 'one-click-maintenance') . $e->getMessage());
        }
    }
}

// Initialize the plugin
new OneClickMaintenance();
