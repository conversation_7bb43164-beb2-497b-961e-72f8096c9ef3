/**
 * One-Click Maintenance Mode - Admin Styles
 * Clean and modern admin interface
 */

.ocm-admin-container {
    max-width: 1200px;
    margin: 20px 0;
}

.ocm-admin-header {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    border-left: 4px solid #0073aa;
}

.ocm-admin-header h1 {
    margin: 0 0 10px 0;
    color: #23282d;
    font-size: 28px;
    font-weight: 600;
}

.ocm-admin-header p {
    margin: 0 0 20px 0;
    color: #666;
    font-size: 16px;
}

.ocm-header-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.ocm-preview-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    font-size: 14px;
    text-decoration: none;
}

.ocm-preview-button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Tab Navigation */
.ocm-tab-wrapper {
    margin: 30px 0;
    border-bottom: 1px solid #ccd0d4;
}

.ocm-tab-wrapper .nav-tab {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    border: 1px solid transparent;
    border-bottom: none;
    background: #f1f1f1;
    color: #555;
    transition: all 0.3s ease;
}

.ocm-tab-wrapper .nav-tab:hover {
    background: #e8e8e8;
    color: #333;
}

.ocm-tab-wrapper .nav-tab.nav-tab-active {
    background: #fff;
    color: #0073aa;
    border-color: #ccd0d4;
    border-bottom-color: #fff;
    margin-bottom: -1px;
}

.ocm-tab-wrapper .nav-tab .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.ocm-admin-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.ocm-settings-panel {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.ocm-preview-panel {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 32px;
    height: fit-content;
}

.ocm-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #e1e1e1;
}

.ocm-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.ocm-section-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #23282d;
    display: flex;
    align-items: center;
    gap: 10px;
}

.ocm-section-title::before {
    content: '';
    width: 4px;
    height: 20px;
    background: #0073aa;
    border-radius: 2px;
}

.ocm-field-group {
    margin-bottom: 25px;
}

.ocm-field-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #23282d;
}

.ocm-field-group input[type="text"],
.ocm-field-group input[type="url"],
.ocm-field-group input[type="datetime-local"],
.ocm-field-group textarea,
.ocm-field-group select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.ocm-field-group input:focus,
.ocm-field-group textarea:focus,
.ocm-field-group select:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
}

.ocm-field-group textarea {
    min-height: 100px;
    resize: vertical;
}

.ocm-toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.ocm-toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.ocm-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 34px;
}

.ocm-toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .ocm-toggle-slider {
    background-color: #0073aa;
}

input:checked + .ocm-toggle-slider:before {
    transform: translateX(26px);
}

.ocm-color-picker-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
}

.ocm-upload-button,
.ocm-remove-button {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: #f7f7f7;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.3s ease;
}

.ocm-upload-button:hover,
.ocm-remove-button:hover {
    background: #e7e7e7;
    border-color: #ccc;
}

.ocm-image-preview {
    margin-top: 10px;
    max-width: 200px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.ocm-radio-group {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.ocm-radio-option {
    display: flex;
    align-items: center;
    gap: 8px;
}

.ocm-checkbox-option {
    display: block;
    margin: 8px 0;
    padding: 8px 12px;
    background: #f9f9f9;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.ocm-checkbox-option:hover {
    background: #f0f0f0;
}

.ocm-checkbox-option input[type="checkbox"] {
    margin-right: 8px;
}

.ocm-developer-hooks {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid #0073aa;
    font-family: monospace;
    font-size: 13px;
    line-height: 1.8;
}

.ocm-developer-hooks code {
    background: #fff;
    padding: 2px 6px;
    border-radius: 3px;
    color: #d63384;
    font-weight: 600;
}

.ocm-preview-frame {
    border: 1px solid #ddd;
    border-radius: 8px;
    min-height: 400px;
    background: #f9f9f9;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-style: italic;
}

.ocm-status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.ocm-status-active {
    background: #d4edda;
    color: #155724;
}

.ocm-status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.ocm-status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

.ocm-save-button {
    background: #0073aa;
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.ocm-save-button:hover {
    background: #005a87;
}

.ocm-save-button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.ocm-field-description {
    font-size: 13px;
    color: #666;
    margin-top: 5px;
    font-style: italic;
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spin {
    animation: spin 1s linear infinite;
}

/* Responsive design */
@media (max-width: 1024px) {
    .ocm-admin-content {
        grid-template-columns: 1fr;
    }

    .ocm-preview-panel {
        position: static;
    }

    .ocm-tab-wrapper .nav-tab {
        padding: 10px 15px;
        font-size: 13px;
    }

    .ocm-header-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .ocm-admin-header,
    .ocm-settings-panel,
    .ocm-preview-panel {
        padding: 20px;
    }
    
    .ocm-radio-group {
        flex-direction: column;
        gap: 10px;
    }
}
