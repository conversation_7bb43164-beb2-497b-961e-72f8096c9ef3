/**
 * One-Click Maintenance Mode - Email Capture
 * Frontend email capture functionality
 */

(function($) {
    'use strict';
    
    $(document).ready(function() {
        
        // Email capture form submission
        $('#ocm-email-form').on('submit', function(e) {
            e.preventDefault();
            
            const form = $(this);
            const emailInput = $('#ocm-email-input');
            const submitButton = $('.ocm-email-submit');
            const submitText = $('.ocm-submit-text');
            const submitLoading = $('.ocm-submit-loading');
            const messageDiv = $('#ocm-email-message');
            
            const email = emailInput.val().trim();
            
            // Basic validation
            if (!email) {
                showMessage('Please enter your email address.', 'error');
                return;
            }
            
            if (!isValidEmail(email)) {
                showMessage('Please enter a valid email address.', 'error');
                return;
            }
            
            // Show loading state
            submitButton.prop('disabled', true);
            submitText.hide();
            submitLoading.show();
            messageDiv.hide();
            
            // Make AJAX request
            $.ajax({
                url: ocm_email.ajax_url || '/wp-admin/admin-ajax.php',
                type: 'POST',
                data: {
                    action: 'ocm_email_capture',
                    email: email,
                    nonce: ocm_email.nonce || ''
                },
                success: function(response) {
                    if (response.success) {
                        showMessage(response.data, 'success');
                        emailInput.val(''); // Clear the input
                        
                        // Add success animation
                        form.addClass('ocm-success-animation');
                        
                        // Optional: Hide form after success
                        setTimeout(function() {
                            form.fadeOut(300, function() {
                                $(this).replaceWith('<div class="ocm-email-success"><p>' + response.data + '</p></div>');
                            });
                        }, 2000);
                        
                    } else {
                        showMessage(response.data || 'An error occurred. Please try again.', 'error');
                    }
                },
                error: function() {
                    showMessage('Network error. Please check your connection and try again.', 'error');
                },
                complete: function() {
                    // Reset button state
                    submitButton.prop('disabled', false);
                    submitText.show();
                    submitLoading.hide();
                }
            });
        });
        
        // Email input validation on blur
        $('#ocm-email-input').on('blur', function() {
            const email = $(this).val().trim();
            if (email && !isValidEmail(email)) {
                $(this).addClass('ocm-invalid');
                showMessage('Please enter a valid email address.', 'error');
            } else {
                $(this).removeClass('ocm-invalid');
                $('#ocm-email-message').hide();
            }
        });
        
        // Clear validation on focus
        $('#ocm-email-input').on('focus', function() {
            $(this).removeClass('ocm-invalid');
            $('#ocm-email-message').hide();
        });
        
        // Helper function to validate email
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        // Helper function to show messages
        function showMessage(message, type) {
            const messageDiv = $('#ocm-email-message');
            messageDiv
                .removeClass('success error')
                .addClass(type)
                .text(message)
                .fadeIn(300);
        }
        
        // Add some visual enhancements
        addEmailCaptureEnhancements();
        
        function addEmailCaptureEnhancements() {
            // Add focus effects
            $('#ocm-email-input').on('focus', function() {
                $(this).closest('.ocm-email-input-group').addClass('focused');
            }).on('blur', function() {
                $(this).closest('.ocm-email-input-group').removeClass('focused');
            });
            
            // Add hover effects to submit button
            $('.ocm-email-submit').on('mouseenter', function() {
                $(this).addClass('hover');
            }).on('mouseleave', function() {
                $(this).removeClass('hover');
            });
            
            // Add CSS for enhancements
            const style = document.createElement('style');
            style.textContent = `
                .ocm-email-input-group.focused {
                    transform: scale(1.02);
                    transition: transform 0.3s ease;
                }
                
                .ocm-email-input-group input.ocm-invalid {
                    border-color: #e74c3c !important;
                    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.2) !important;
                }
                
                .ocm-email-submit.hover {
                    transform: translateY(-1px);
                }
                
                .ocm-success-animation {
                    animation: successPulse 0.6s ease-out;
                }
                
                @keyframes successPulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }
                
                .ocm-email-success {
                    padding: 20px;
                    background: rgba(46, 204, 113, 0.1);
                    border: 2px solid rgba(46, 204, 113, 0.3);
                    border-radius: 10px;
                    text-align: center;
                    color: #27ae60;
                    font-weight: 500;
                    animation: fadeInUp 0.5s ease-out;
                }
                
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                
                .ocm-email-message {
                    animation: slideDown 0.3s ease-out;
                }
                
                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        // Keyboard accessibility
        $('#ocm-email-input').on('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                $('#ocm-email-form').submit();
            }
        });
        
        // Add ARIA labels for accessibility
        $('#ocm-email-input').attr('aria-label', 'Email address for maintenance notifications');
        $('.ocm-email-submit').attr('aria-label', 'Subscribe for maintenance notifications');
        
    });
    
})(jQuery);
