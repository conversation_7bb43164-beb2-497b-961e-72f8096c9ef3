<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow">
    <title><?php echo esc_html(get_option('ocm_title', __('We\'re Under Maintenance', 'one-click-maintenance'))); ?> - <?php bloginfo('name'); ?></title>

    <!-- Open Graph / Social Media Meta Tags -->
    <meta property="og:title" content="<?php echo esc_attr($title); ?> - <?php bloginfo('name'); ?>">
    <meta property="og:description" content="<?php echo esc_attr(wp_strip_all_tags($description)); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo esc_url(home_url()); ?>">
    <?php if (!empty($meta_image)): ?>
    <meta property="og:image" content="<?php echo esc_url($meta_image); ?>">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <?php endif; ?>

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo esc_attr($title); ?> - <?php bloginfo('name'); ?>">
    <meta name="twitter:description" content="<?php echo esc_attr(wp_strip_all_tags($description)); ?>">
    <?php if (!empty($meta_image)): ?>
    <meta name="twitter:image" content="<?php echo esc_url($meta_image); ?>">
    <?php endif; ?>

    <!-- Custom Favicon -->
    <?php if (!empty($custom_favicon)): ?>
    <link rel="icon" type="image/x-icon" href="<?php echo esc_url($custom_favicon); ?>">
    <link rel="shortcut icon" href="<?php echo esc_url($custom_favicon); ?>">
    <?php endif; ?>
    
    <?php
    // Get settings
    $title = get_option('ocm_title', __('We\'re Under Maintenance', 'one-click-maintenance'));
    $description = get_option('ocm_description', __('We are currently performing scheduled maintenance. We will be back online shortly.', 'one-click-maintenance'));
    $font_family = get_option('ocm_font_family', 'DM Sans');
    $font_size = get_option('ocm_font_size', 'medium');
    $background_type = get_option('ocm_background_type', 'color');
    $background_color = get_option('ocm_background_color', '#f8f9fa');
    $background_image = get_option('ocm_background_image', '');
    $logo = get_option('ocm_logo', '');
    $countdown_enabled = get_option('ocm_countdown_enabled', false);
    $countdown_date = get_option('ocm_countdown_date', '');
    $footer_message = get_option('ocm_footer_message', '');
    $custom_css = get_option('ocm_custom_css', '');

    // Engagement & Branding settings
    $email_capture_enabled = get_option('ocm_email_capture_enabled', false);
    $social_links = get_option('ocm_social_links', array());
    $custom_favicon = get_option('ocm_custom_favicon', '');
    $meta_image = get_option('ocm_meta_image', '');

    // Advanced settings
    $shortcode_support = get_option('ocm_shortcode_support', false);
    
    // Enqueue styles
    wp_enqueue_style('ocm-frontend-css', OCM_PLUGIN_URL . 'css/style.css', array(), OCM_PLUGIN_VERSION);
    
    // Enqueue Google Fonts if needed
    if ($font_family !== 'inherit') {
        $font_url = 'https://fonts.googleapis.com/css2?family=' . urlencode($font_family) . ':wght@300;400;500;600;700&display=swap';
        wp_enqueue_style('ocm-google-fonts', $font_url, array(), null);
    }

    // Enqueue jQuery for AJAX functionality
    wp_enqueue_script('jquery');

    // Print styles and scripts
    wp_print_styles();
    wp_print_scripts();
    ?>
    
    <style>
        <?php if ($font_family !== 'inherit'): ?>
        html, body {
            font-family: '<?php echo esc_attr($font_family); ?>', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        }
        <?php endif; ?>
        
        .ocm-maintenance-container {
            <?php if ($background_type === 'color'): ?>
                background-color: <?php echo esc_attr($background_color); ?>;
            <?php elseif ($background_type === 'image' && !empty($background_image)): ?>
                background-image: url('<?php echo esc_url($background_image); ?>');
            <?php endif; ?>
        }
        
        <?php if (!empty($custom_css)): ?>
        /* Custom CSS */
        <?php echo wp_strip_all_tags($custom_css); ?>
        <?php endif; ?>
    </style>
    
    <?php if ($countdown_enabled && !empty($countdown_date)): ?>
    <script>
        // Countdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            const countdownDate = new Date('<?php echo esc_js($countdown_date); ?>').getTime();
            
            function updateCountdown() {
                const now = new Date().getTime();
                const distance = countdownDate - now;
                
                if (distance < 0) {
                    document.getElementById('ocm-countdown').innerHTML = '<div class="ocm-countdown-expired"><?php echo esc_js(__('Maintenance completed!', 'one-click-maintenance')); ?></div>';
                    return;
                }
                
                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);
                
                document.getElementById('ocm-days').textContent = days.toString().padStart(2, '0');
                document.getElementById('ocm-hours').textContent = hours.toString().padStart(2, '0');
                document.getElementById('ocm-minutes').textContent = minutes.toString().padStart(2, '0');
                document.getElementById('ocm-seconds').textContent = seconds.toString().padStart(2, '0');
            }
            
            updateCountdown();
            setInterval(updateCountdown, 1000);
        });
    </script>
    <?php endif; ?>
</head>
<body class="ocm-font-<?php echo esc_attr($font_size); ?>">
    <?php do_action('ocm_before_maintenance_page'); ?>

    <div class="ocm-maintenance-container">
        <div class="ocm-content">
            <?php do_action('ocm_before_maintenance_content'); ?>
            <?php if (!empty($logo)): ?>
            <div class="ocm-logo">
                <img src="<?php echo esc_url($logo); ?>" alt="<?php bloginfo('name'); ?>">
            </div>
            <?php endif; ?>
            
            <?php if (!empty($title)): ?>
            <h1 class="ocm-title"><?php echo esc_html(apply_filters('ocm_maintenance_title', $title)); ?></h1>
            <?php endif; ?>
            
            <?php if (!empty($description)): ?>
            <div class="ocm-description">
                <?php
                $processed_description = $shortcode_support ? do_shortcode($description) : $description;
                $filtered_description = apply_filters('ocm_maintenance_description', $processed_description);
                echo wp_kses_post(wpautop($filtered_description));
                ?>
            </div>
            <?php endif; ?>
            
            <?php if ($countdown_enabled && !empty($countdown_date)): ?>
            <div class="ocm-countdown" id="ocm-countdown">
                <div class="ocm-countdown-title">
                    <?php _e('We\'ll be back in:', 'one-click-maintenance'); ?>
                </div>
                <div class="ocm-countdown-timer">
                    <div class="ocm-countdown-item">
                        <span class="ocm-countdown-number" id="ocm-days">00</span>
                        <span class="ocm-countdown-label"><?php _e('Days', 'one-click-maintenance'); ?></span>
                    </div>
                    <div class="ocm-countdown-item">
                        <span class="ocm-countdown-number" id="ocm-hours">00</span>
                        <span class="ocm-countdown-label"><?php _e('Hours', 'one-click-maintenance'); ?></span>
                    </div>
                    <div class="ocm-countdown-item">
                        <span class="ocm-countdown-number" id="ocm-minutes">00</span>
                        <span class="ocm-countdown-label"><?php _e('Minutes', 'one-click-maintenance'); ?></span>
                    </div>
                    <div class="ocm-countdown-item">
                        <span class="ocm-countdown-number" id="ocm-seconds">00</span>
                        <span class="ocm-countdown-label"><?php _e('Seconds', 'one-click-maintenance'); ?></span>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if ($email_capture_enabled): ?>
            <div class="ocm-email-capture">
                <h3 class="ocm-email-title"><?php _e('Get Notified When We\'re Back', 'one-click-maintenance'); ?></h3>
                <p class="ocm-email-description"><?php _e('Enter your email to be notified when we\'re back online.', 'one-click-maintenance'); ?></p>
                <form class="ocm-email-form" id="ocm-email-form">
                    <div class="ocm-email-input-group">
                        <input type="email" id="ocm-email-input" placeholder="<?php _e('Enter your email address', 'one-click-maintenance'); ?>" required>
                        <button type="submit" class="ocm-email-submit">
                            <span class="ocm-submit-text"><?php _e('Notify Me', 'one-click-maintenance'); ?></span>
                            <span class="ocm-submit-loading" style="display: none;"><?php _e('Subscribing...', 'one-click-maintenance'); ?></span>
                        </button>
                    </div>
                    <div class="ocm-email-message" id="ocm-email-message"></div>
                </form>
            </div>
            <?php endif; ?>

            <?php if (!empty(array_filter($social_links))): ?>
            <div class="ocm-social-links">
                <h4 class="ocm-social-title"><?php _e('Follow Us', 'one-click-maintenance'); ?></h4>
                <div class="ocm-social-icons">
                    <?php if (!empty($social_links['facebook'])): ?>
                    <a href="<?php echo esc_url($social_links['facebook']); ?>" target="_blank" rel="noopener" class="ocm-social-link ocm-facebook" aria-label="Facebook">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($social_links['twitter'])): ?>
                    <a href="<?php echo esc_url($social_links['twitter']); ?>" target="_blank" rel="noopener" class="ocm-social-link ocm-twitter" aria-label="Twitter/X">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                        </svg>
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($social_links['linkedin'])): ?>
                    <a href="<?php echo esc_url($social_links['linkedin']); ?>" target="_blank" rel="noopener" class="ocm-social-link ocm-linkedin" aria-label="LinkedIn">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($social_links['instagram'])): ?>
                    <a href="<?php echo esc_url($social_links['instagram']); ?>" target="_blank" rel="noopener" class="ocm-social-link ocm-instagram" aria-label="Instagram">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if (!empty($footer_message)): ?>
            <div class="ocm-footer">
                <?php
                $processed_footer = $shortcode_support ? do_shortcode($footer_message) : $footer_message;
                echo wp_kses_post(wpautop($processed_footer));
                ?>
            </div>
            <?php endif; ?>

            <?php do_action('ocm_after_maintenance_content'); ?>
        </div>
    </div>

    <?php do_action('ocm_after_maintenance_page'); ?>
</body>
</html>
