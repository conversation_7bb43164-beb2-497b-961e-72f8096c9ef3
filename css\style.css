/**
 * One-Click Maintenance Mode - Frontend Styles
 * Modern, clean design for the maintenance page
 */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: 'DM Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

/* Main maintenance container */
.ocm-maintenance-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

/* Background overlay for better text readability */
.ocm-maintenance-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
}

/* Content wrapper */
.ocm-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 600px;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    padding: 60px 40px;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* Logo styles */
.ocm-logo {
    margin-bottom: 30px;
}

.ocm-logo img {
    max-width: 200px;
    max-height: 80px;
    width: auto;
    height: auto;
}

/* Title styles */
.ocm-title {
    font-weight: 600;
    margin-bottom: 20px;
    color: #2c3e50;
    line-height: 1.2;
}

/* Font size variations */
.ocm-font-small .ocm-title { font-size: 28px; }
.ocm-font-medium .ocm-title { font-size: 36px; }
.ocm-font-large .ocm-title { font-size: 48px; }

.ocm-font-small .ocm-description { font-size: 16px; }
.ocm-font-medium .ocm-description { font-size: 18px; }
.ocm-font-large .ocm-description { font-size: 22px; }

/* Description styles */
.ocm-description {
    color: #5a6c7d;
    margin-bottom: 40px;
    line-height: 1.7;
}

/* Countdown timer */
.ocm-countdown {
    margin: 40px 0;
    padding: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    color: white;
}

.ocm-countdown-title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
    opacity: 0.9;
}

.ocm-countdown-timer {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.ocm-countdown-item {
    text-align: center;
    min-width: 80px;
}

.ocm-countdown-number {
    display: block;
    font-size: 32px;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 5px;
}

.ocm-countdown-label {
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.8;
}

/* Footer message */
.ocm-footer {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #e1e8ed;
    color: #7f8c8d;
    font-size: 14px;
    line-height: 1.6;
}

.ocm-footer a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.ocm-footer a:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* Responsive design */
@media (max-width: 768px) {
    .ocm-content {
        padding: 40px 30px;
        margin: 20px;
        border-radius: 15px;
    }
    
    .ocm-font-small .ocm-title { font-size: 24px; }
    .ocm-font-medium .ocm-title { font-size: 28px; }
    .ocm-font-large .ocm-title { font-size: 36px; }
    
    .ocm-font-small .ocm-description { font-size: 14px; }
    .ocm-font-medium .ocm-description { font-size: 16px; }
    .ocm-font-large .ocm-description { font-size: 18px; }
    
    .ocm-countdown-timer {
        gap: 15px;
    }
    
    .ocm-countdown-item {
        min-width: 60px;
    }
    
    .ocm-countdown-number {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .ocm-content {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .ocm-countdown {
        padding: 20px;
    }
    
    .ocm-countdown-timer {
        gap: 10px;
    }
    
    .ocm-countdown-item {
        min-width: 50px;
    }
    
    .ocm-countdown-number {
        font-size: 20px;
    }
    
    .ocm-countdown-label {
        font-size: 10px;
    }
}

/* Animation for smooth loading */
.ocm-content {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom CSS placeholder */
.ocm-custom-styles {
    /* Custom styles will be injected here */
}
