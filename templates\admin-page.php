<?php
/**
 * Admin Settings Page Template - Tabbed Interface
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current tab
$current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'general';

// Handle form submission
if (isset($_POST['submit']) && wp_verify_nonce($_POST['ocm_nonce'], 'ocm_save_settings')) {
    $errors = array();
    $success_message = '';
    $settings = array(
        // General Settings
        'ocm_maintenance_enabled',
        'ocm_bypass_roles',
        'ocm_send_503_header',

        // Content Settings
        'ocm_title',
        'ocm_description',
        'ocm_footer_message',

        // Design Settings
        'ocm_font_family',
        'ocm_font_size',
        'ocm_background_type',
        'ocm_background_color',
        'ocm_background_image',
        'ocm_logo',
        'ocm_custom_css',

        // Countdown Settings
        'ocm_countdown_enabled',
        'ocm_countdown_date',

        // Engagement & Branding
        'ocm_email_capture_enabled',
        'ocm_social_links',
        'ocm_custom_favicon',
        'ocm_meta_image',

        // Advanced Settings
        'ocm_shortcode_support',
        'ocm_multisite_mode'
    );

    foreach ($settings as $setting) {
        if (isset($_POST[$setting])) {
            $value = $_POST[$setting];

            // Validation and sanitization
            try {
                // Handle arrays (like bypass_roles and social_links)
                if ($setting === 'ocm_bypass_roles') {
                    $value = is_array($value) ? array_map('sanitize_text_field', $value) : array();
                    if (empty($value)) {
                        $errors[] = __('At least one user role must be selected to bypass maintenance mode.', 'one-click-maintenance');
                        continue;
                    }
                }
                elseif ($setting === 'ocm_social_links') {
                    $value = is_array($value) ? array_map('esc_url_raw', array_filter($value)) : array();
                }
                // Handle countdown date validation
                elseif ($setting === 'ocm_countdown_date') {
                    $value = sanitize_text_field($value);
                    if (!empty($value) && strtotime($value) <= current_time('timestamp')) {
                        $errors[] = __('Countdown end date must be in the future.', 'one-click-maintenance');
                        continue;
                    }
                }
                // Handle URL fields
                elseif (in_array($setting, array('ocm_background_image', 'ocm_logo', 'ocm_custom_favicon', 'ocm_meta_image'))) {
                    $value = esc_url_raw($value);
                }
                // Handle rich text fields
                elseif (in_array($setting, array('ocm_description', 'ocm_footer_message'))) {
                    $value = wp_kses_post($value);
                }
                // Handle CSS field
                elseif ($setting === 'ocm_custom_css') {
                    $value = wp_strip_all_tags($value);
                    // Basic CSS validation
                    if (!empty($value) && strpos($value, '<script') !== false) {
                        $errors[] = __('Custom CSS cannot contain script tags for security reasons.', 'one-click-maintenance');
                        continue;
                    }
                }
                // Handle regular text fields
                else {
                    $value = sanitize_text_field($value);
                }

                update_option($setting, $value);

            } catch (Exception $e) {
                $errors[] = sprintf(__('Error saving %s: %s', 'one-click-maintenance'), $setting, $e->getMessage());
            }
        } else {
            // Handle checkboxes that might not be posted
            $checkbox_settings = array(
                'ocm_maintenance_enabled',
                'ocm_send_503_header',
                'ocm_countdown_enabled',
                'ocm_email_capture_enabled',
                'ocm_shortcode_support',
                'ocm_multisite_mode'
            );

            if (in_array($setting, $checkbox_settings)) {
                update_option($setting, false);
            }
        }
    }

    // Display messages
    if (!empty($errors)) {
        echo '<div class="notice notice-error"><ul>';
        foreach ($errors as $error) {
            echo '<li>' . esc_html($error) . '</li>';
        }
        echo '</ul></div>';
    }

    if (empty($errors)) {
        $success_message = __('Settings saved successfully!', 'one-click-maintenance');
        echo '<div class="notice notice-success"><p>' . esc_html($success_message) . '</p></div>';
    }
}

// Get current settings
$maintenance_enabled = get_option('ocm_maintenance_enabled', false);
$bypass_roles = get_option('ocm_bypass_roles', array('administrator'));
$send_503_header = get_option('ocm_send_503_header', true);
$title = get_option('ocm_title', '');
$description = get_option('ocm_description', '');
$footer_message = get_option('ocm_footer_message', '');
$font_family = get_option('ocm_font_family', 'DM Sans');
$font_size = get_option('ocm_font_size', 'medium');
$background_type = get_option('ocm_background_type', 'color');
$background_color = get_option('ocm_background_color', '#f8f9fa');
$background_image = get_option('ocm_background_image', '');
$logo = get_option('ocm_logo', '');
$custom_css = get_option('ocm_custom_css', '');
$countdown_enabled = get_option('ocm_countdown_enabled', false);
$countdown_date = get_option('ocm_countdown_date', '');
$email_capture_enabled = get_option('ocm_email_capture_enabled', false);
$social_links = get_option('ocm_social_links', array());
$custom_favicon = get_option('ocm_custom_favicon', '');
$meta_image = get_option('ocm_meta_image', '');
$shortcode_support = get_option('ocm_shortcode_support', false);
$multisite_mode = get_option('ocm_multisite_mode', false);
?>

<div class="wrap ocm-admin-container">
    <div class="ocm-admin-header">
        <h1><?php _e('One-Click Maintenance Mode', 'one-click-maintenance'); ?></h1>
        <p><?php _e('Easily activate a maintenance mode screen with one click, customizable content, and modern clean design.', 'one-click-maintenance'); ?></p>

        <div class="ocm-header-controls">
            <div class="ocm-status-indicator <?php echo $maintenance_enabled ? 'ocm-status-active' : 'ocm-status-inactive'; ?>">
                <span class="ocm-status-dot"></span>
                <?php echo $maintenance_enabled ? __('Maintenance Mode Active', 'one-click-maintenance') : __('Maintenance Mode Inactive', 'one-click-maintenance'); ?>
            </div>

            <button type="button" class="button button-secondary ocm-preview-button" id="ocm-preview-btn">
                <span class="dashicons dashicons-visibility"></span>
                <?php _e('Preview Maintenance Page', 'one-click-maintenance'); ?>
            </button>
        </div>
    </div>

    <!-- Tab Navigation -->
    <nav class="nav-tab-wrapper ocm-tab-wrapper">
        <a href="?page=one-click-maintenance&tab=general" class="nav-tab <?php echo $current_tab === 'general' ? 'nav-tab-active' : ''; ?>">
            <span class="dashicons dashicons-admin-settings"></span>
            <?php _e('General Settings', 'one-click-maintenance'); ?>
        </a>
        <a href="?page=one-click-maintenance&tab=content" class="nav-tab <?php echo $current_tab === 'content' ? 'nav-tab-active' : ''; ?>">
            <span class="dashicons dashicons-edit-page"></span>
            <?php _e('Content & Design', 'one-click-maintenance'); ?>
        </a>
        <a href="?page=one-click-maintenance&tab=engagement" class="nav-tab <?php echo $current_tab === 'engagement' ? 'nav-tab-active' : ''; ?>">
            <span class="dashicons dashicons-heart"></span>
            <?php _e('Engagement & Branding', 'one-click-maintenance'); ?>
        </a>
        <a href="?page=one-click-maintenance&tab=advanced" class="nav-tab <?php echo $current_tab === 'advanced' ? 'nav-tab-active' : ''; ?>">
            <span class="dashicons dashicons-admin-tools"></span>
            <?php _e('Advanced Settings', 'one-click-maintenance'); ?>
        </a>
    </nav>

    <div class="ocm-admin-content">
        <div class="ocm-settings-panel">
            <form method="post" action="">
                <?php wp_nonce_field('ocm_save_settings', 'ocm_nonce'); ?>

                <?php if ($current_tab === 'general'): ?>
                <!-- General Settings Tab -->
                <div class="ocm-section">
                    <h2 class="ocm-section-title"><?php _e('Maintenance Mode Control', 'one-click-maintenance'); ?></h2>

                    <div class="ocm-field-group">
                        <label for="ocm_maintenance_enabled">
                            <?php _e('Enable Maintenance Mode', 'one-click-maintenance'); ?>
                        </label>
                        <label class="ocm-toggle-switch">
                            <input type="checkbox" id="ocm_maintenance_enabled" name="ocm_maintenance_enabled" value="1" <?php checked($maintenance_enabled); ?>>
                            <span class="ocm-toggle-slider"></span>
                        </label>
                        <p class="ocm-field-description"><?php _e('When enabled, visitors will see the maintenance page. Administrators can still access the site.', 'one-click-maintenance'); ?></p>
                    </div>
                </div>

                <div class="ocm-section">
                    <h2 class="ocm-section-title"><?php _e('User Access Control', 'one-click-maintenance'); ?></h2>

                    <div class="ocm-field-group">
                        <label><?php _e('User Roles That Can Bypass Maintenance Mode', 'one-click-maintenance'); ?></label>
                        <?php
                        $available_roles = wp_roles()->get_names();
                        foreach ($available_roles as $role_key => $role_name):
                        ?>
                        <label class="ocm-checkbox-option">
                            <input type="checkbox" name="ocm_bypass_roles[]" value="<?php echo esc_attr($role_key); ?>" <?php checked(in_array($role_key, $bypass_roles)); ?>>
                            <?php echo esc_html($role_name); ?>
                        </label>
                        <?php endforeach; ?>
                        <p class="ocm-field-description"><?php _e('Selected user roles will be able to access the site normally while maintenance mode is active.', 'one-click-maintenance'); ?></p>
                    </div>
                </div>

                <div class="ocm-section">
                    <h2 class="ocm-section-title"><?php _e('SEO Settings', 'one-click-maintenance'); ?></h2>

                    <div class="ocm-field-group">
                        <label for="ocm_send_503_header">
                            <?php _e('Send 503 Service Unavailable Header', 'one-click-maintenance'); ?>
                        </label>
                        <label class="ocm-toggle-switch">
                            <input type="checkbox" id="ocm_send_503_header" name="ocm_send_503_header" value="1" <?php checked($send_503_header); ?>>
                            <span class="ocm-toggle-slider"></span>
                        </label>
                        <p class="ocm-field-description"><?php _e('Recommended: Tells search engines that the maintenance is temporary and they should check back later.', 'one-click-maintenance'); ?></p>
                    </div>
                </div>

                <?php endif; ?>

                <?php if ($current_tab === 'content'): ?>
                <!-- Content & Design Tab -->
                <div class="ocm-section">
                    <h2 class="ocm-section-title"><?php _e('Content Settings', 'one-click-maintenance'); ?></h2>

                    <div class="ocm-field-group">
                        <label for="ocm_title"><?php _e('Title', 'one-click-maintenance'); ?></label>
                        <input type="text" id="ocm_title" name="ocm_title" value="<?php echo esc_attr($title); ?>" placeholder="<?php _e('We\'re Under Maintenance', 'one-click-maintenance'); ?>">
                    </div>

                    <div class="ocm-field-group">
                        <label for="ocm_description"><?php _e('Description', 'one-click-maintenance'); ?></label>
                        <textarea id="ocm_description" name="ocm_description" placeholder="<?php _e('We are currently performing scheduled maintenance...', 'one-click-maintenance'); ?>"><?php echo esc_textarea($description); ?></textarea>
                    </div>

                    <div class="ocm-field-group">
                        <label for="ocm_footer_message"><?php _e('Footer Message', 'one-click-maintenance'); ?></label>
                        <textarea id="ocm_footer_message" name="ocm_footer_message" placeholder="<?php _e('For urgent matters, please contact us...', 'one-click-maintenance'); ?>"><?php echo esc_textarea($footer_message); ?></textarea>
                    </div>
                </div>

                <div class="ocm-section">
                    <h2 class="ocm-section-title"><?php _e('Countdown Timer', 'one-click-maintenance'); ?></h2>

                    <div class="ocm-field-group">
                        <label for="ocm_countdown_enabled">
                            <?php _e('Enable Countdown Timer', 'one-click-maintenance'); ?>
                        </label>
                        <label class="ocm-toggle-switch">
                            <input type="checkbox" id="ocm_countdown_enabled" name="ocm_countdown_enabled" value="1" <?php checked($countdown_enabled); ?>>
                            <span class="ocm-toggle-slider"></span>
                        </label>
                        <p class="ocm-field-description"><?php _e('Show a countdown timer that automatically disables maintenance mode when it reaches zero.', 'one-click-maintenance'); ?></p>
                    </div>

                    <div class="ocm-field-group" id="ocm-countdown-date-field">
                        <label for="ocm_countdown_date"><?php _e('End Date & Time', 'one-click-maintenance'); ?></label>
                        <input type="datetime-local" id="ocm_countdown_date" name="ocm_countdown_date" value="<?php echo esc_attr($countdown_date); ?>">
                        <p class="ocm-field-description"><?php _e('Set when the maintenance is expected to end. The website will automatically go live at this time.', 'one-click-maintenance'); ?></p>
                    </div>
                </div>

                <?php endif; ?>

                <?php if ($current_tab === 'content'): ?>
                <!-- Design Settings -->
                <div class="ocm-section">
                    <h2 class="ocm-section-title"><?php _e('Design Settings', 'one-click-maintenance'); ?></h2>

                    <div class="ocm-field-group">
                        <label for="ocm_font_family"><?php _e('Font Family', 'one-click-maintenance'); ?></label>
                        <select id="ocm_font_family" name="ocm_font_family">
                            <option value="DM Sans" <?php selected($font_family, 'DM Sans'); ?>>DM Sans</option>
                            <option value="Inter" <?php selected($font_family, 'Inter'); ?>>Inter</option>
                            <option value="Roboto" <?php selected($font_family, 'Roboto'); ?>>Roboto</option>
                            <option value="Open Sans" <?php selected($font_family, 'Open Sans'); ?>>Open Sans</option>
                            <option value="Lato" <?php selected($font_family, 'Lato'); ?>>Lato</option>
                            <option value="Poppins" <?php selected($font_family, 'Poppins'); ?>>Poppins</option>
                            <option value="inherit" <?php selected($font_family, 'inherit'); ?>><?php _e('Use Theme Font', 'one-click-maintenance'); ?></option>
                        </select>
                    </div>
                    
                    <div class="ocm-field-group">
                        <label><?php _e('Font Size', 'one-click-maintenance'); ?></label>
                        <div class="ocm-radio-group">
                            <label class="ocm-radio-option">
                                <input type="radio" name="ocm_font_size" value="small" <?php checked($font_size, 'small'); ?>>
                                <?php _e('Small', 'one-click-maintenance'); ?>
                            </label>
                            <label class="ocm-radio-option">
                                <input type="radio" name="ocm_font_size" value="medium" <?php checked($font_size, 'medium'); ?>>
                                <?php _e('Medium', 'one-click-maintenance'); ?>
                            </label>
                            <label class="ocm-radio-option">
                                <input type="radio" name="ocm_font_size" value="large" <?php checked($font_size, 'large'); ?>>
                                <?php _e('Large', 'one-click-maintenance'); ?>
                            </label>
                        </div>
                    </div>
                    
                    <div class="ocm-field-group">
                        <label><?php _e('Background Type', 'one-click-maintenance'); ?></label>
                        <div class="ocm-radio-group">
                            <label class="ocm-radio-option">
                                <input type="radio" name="ocm_background_type" value="color" <?php checked($background_type, 'color'); ?>>
                                <?php _e('Color', 'one-click-maintenance'); ?>
                            </label>
                            <label class="ocm-radio-option">
                                <input type="radio" name="ocm_background_type" value="image" <?php checked($background_type, 'image'); ?>>
                                <?php _e('Image', 'one-click-maintenance'); ?>
                            </label>
                        </div>
                    </div>
                    
                    <div class="ocm-field-group" id="ocm-background-color-field">
                        <label for="ocm_background_color"><?php _e('Background Color', 'one-click-maintenance'); ?></label>
                        <div class="ocm-color-picker-wrapper">
                            <input type="text" id="ocm_background_color" name="ocm_background_color" value="<?php echo esc_attr($background_color); ?>" class="ocm-color-picker">
                        </div>
                    </div>
                    
                    <div class="ocm-field-group" id="ocm-background-image-field">
                        <label for="ocm_background_image"><?php _e('Background Image', 'one-click-maintenance'); ?></label>
                        <input type="hidden" id="ocm_background_image" name="ocm_background_image" value="<?php echo esc_attr($background_image); ?>">
                        <button type="button" class="ocm-upload-button" id="ocm-upload-bg"><?php _e('Choose Image', 'one-click-maintenance'); ?></button>
                        <button type="button" class="ocm-remove-button" id="ocm-remove-bg" style="<?php echo empty($background_image) ? 'display:none;' : ''; ?>"><?php _e('Remove', 'one-click-maintenance'); ?></button>
                        <?php if (!empty($background_image)): ?>
                            <img src="<?php echo esc_url($background_image); ?>" class="ocm-image-preview" id="ocm-bg-preview">
                        <?php endif; ?>
                    </div>
                    
                    <div class="ocm-field-group">
                        <label for="ocm_logo"><?php _e('Logo', 'one-click-maintenance'); ?></label>
                        <input type="hidden" id="ocm_logo" name="ocm_logo" value="<?php echo esc_attr($logo); ?>">
                        <button type="button" class="ocm-upload-button" id="ocm-upload-logo"><?php _e('Choose Logo', 'one-click-maintenance'); ?></button>
                        <button type="button" class="ocm-remove-button" id="ocm-remove-logo" style="<?php echo empty($logo) ? 'display:none;' : ''; ?>"><?php _e('Remove', 'one-click-maintenance'); ?></button>
                        <?php if (!empty($logo)): ?>
                            <img src="<?php echo esc_url($logo); ?>" class="ocm-image-preview" id="ocm-logo-preview">
                        <?php endif; ?>
                    </div>
                </div>

                <div class="ocm-section">
                    <h2 class="ocm-section-title"><?php _e('Custom CSS', 'one-click-maintenance'); ?></h2>

                    <div class="ocm-field-group">
                        <label for="ocm_custom_css"><?php _e('Additional CSS', 'one-click-maintenance'); ?></label>
                        <textarea id="ocm_custom_css" name="ocm_custom_css" placeholder="/* Add your custom CSS here */" style="font-family: monospace; height: 150px;"><?php echo esc_textarea($custom_css); ?></textarea>
                        <p class="ocm-field-description"><?php _e('Add custom CSS to further customize the maintenance page appearance.', 'one-click-maintenance'); ?></p>
                    </div>
                </div>

                <?php endif; ?>

                <?php if ($current_tab === 'engagement'): ?>
                <!-- Engagement & Branding Tab -->
                <div class="ocm-section">
                    <h2 class="ocm-section-title"><?php _e('Email Capture', 'one-click-maintenance'); ?></h2>

                    <div class="ocm-field-group">
                        <label for="ocm_email_capture_enabled">
                            <?php _e('Enable Email Capture Form', 'one-click-maintenance'); ?>
                        </label>
                        <label class="ocm-toggle-switch">
                            <input type="checkbox" id="ocm_email_capture_enabled" name="ocm_email_capture_enabled" value="1" <?php checked($email_capture_enabled); ?>>
                            <span class="ocm-toggle-slider"></span>
                        </label>
                        <p class="ocm-field-description"><?php _e('Allow visitors to subscribe for updates during maintenance.', 'one-click-maintenance'); ?></p>
                    </div>
                </div>

                <div class="ocm-section">
                    <h2 class="ocm-section-title"><?php _e('Social Media Links', 'one-click-maintenance'); ?></h2>

                    <div class="ocm-field-group">
                        <label for="ocm_social_facebook"><?php _e('Facebook URL', 'one-click-maintenance'); ?></label>
                        <input type="url" id="ocm_social_facebook" name="ocm_social_links[facebook]" value="<?php echo esc_attr($social_links['facebook'] ?? ''); ?>" placeholder="https://facebook.com/yourpage">
                    </div>

                    <div class="ocm-field-group">
                        <label for="ocm_social_twitter"><?php _e('Twitter/X URL', 'one-click-maintenance'); ?></label>
                        <input type="url" id="ocm_social_twitter" name="ocm_social_links[twitter]" value="<?php echo esc_attr($social_links['twitter'] ?? ''); ?>" placeholder="https://twitter.com/youraccount">
                    </div>

                    <div class="ocm-field-group">
                        <label for="ocm_social_linkedin"><?php _e('LinkedIn URL', 'one-click-maintenance'); ?></label>
                        <input type="url" id="ocm_social_linkedin" name="ocm_social_links[linkedin]" value="<?php echo esc_attr($social_links['linkedin'] ?? ''); ?>" placeholder="https://linkedin.com/company/yourcompany">
                    </div>

                    <div class="ocm-field-group">
                        <label for="ocm_social_instagram"><?php _e('Instagram URL', 'one-click-maintenance'); ?></label>
                        <input type="url" id="ocm_social_instagram" name="ocm_social_links[instagram]" value="<?php echo esc_attr($social_links['instagram'] ?? ''); ?>" placeholder="https://instagram.com/youraccount">
                    </div>
                </div>

                <div class="ocm-section">
                    <h2 class="ocm-section-title"><?php _e('Branding Assets', 'one-click-maintenance'); ?></h2>

                    <div class="ocm-field-group">
                        <label for="ocm_custom_favicon"><?php _e('Custom Favicon', 'one-click-maintenance'); ?></label>
                        <input type="hidden" id="ocm_custom_favicon" name="ocm_custom_favicon" value="<?php echo esc_attr($custom_favicon); ?>">
                        <button type="button" class="ocm-upload-button" id="ocm-upload-favicon"><?php _e('Choose Favicon', 'one-click-maintenance'); ?></button>
                        <button type="button" class="ocm-remove-button" id="ocm-remove-favicon" style="<?php echo empty($custom_favicon) ? 'display:none;' : ''; ?>"><?php _e('Remove', 'one-click-maintenance'); ?></button>
                        <?php if (!empty($custom_favicon)): ?>
                            <img src="<?php echo esc_url($custom_favicon); ?>" class="ocm-image-preview" id="ocm-favicon-preview" style="max-width: 32px; max-height: 32px;">
                        <?php endif; ?>
                        <p class="ocm-field-description"><?php _e('Upload a custom favicon for the maintenance page (16x16 or 32x32 pixels recommended).', 'one-click-maintenance'); ?></p>
                    </div>

                    <div class="ocm-field-group">
                        <label for="ocm_meta_image"><?php _e('Social Sharing Image', 'one-click-maintenance'); ?></label>
                        <input type="hidden" id="ocm_meta_image" name="ocm_meta_image" value="<?php echo esc_attr($meta_image); ?>">
                        <button type="button" class="ocm-upload-button" id="ocm-upload-meta-image"><?php _e('Choose Image', 'one-click-maintenance'); ?></button>
                        <button type="button" class="ocm-remove-button" id="ocm-remove-meta-image" style="<?php echo empty($meta_image) ? 'display:none;' : ''; ?>"><?php _e('Remove', 'one-click-maintenance'); ?></button>
                        <?php if (!empty($meta_image)): ?>
                            <img src="<?php echo esc_url($meta_image); ?>" class="ocm-image-preview" id="ocm-meta-image-preview">
                        <?php endif; ?>
                        <p class="ocm-field-description"><?php _e('Image shown when your maintenance page is shared on social media (1200x630 pixels recommended).', 'one-click-maintenance'); ?></p>
                    </div>
                </div>

                <?php endif; ?>

                <?php if ($current_tab === 'advanced'): ?>
                <!-- Advanced Settings Tab -->
                <div class="ocm-section">
                    <h2 class="ocm-section-title"><?php _e('Developer Options', 'one-click-maintenance'); ?></h2>

                    <div class="ocm-field-group">
                        <label for="ocm_shortcode_support">
                            <?php _e('Enable Shortcode Support', 'one-click-maintenance'); ?>
                        </label>
                        <label class="ocm-toggle-switch">
                            <input type="checkbox" id="ocm_shortcode_support" name="ocm_shortcode_support" value="1" <?php checked($shortcode_support); ?>>
                            <span class="ocm-toggle-slider"></span>
                        </label>
                        <p class="ocm-field-description"><?php _e('Allow shortcodes to be processed in maintenance page content.', 'one-click-maintenance'); ?></p>
                    </div>

                    <?php if (is_multisite()): ?>
                    <div class="ocm-field-group">
                        <label for="ocm_multisite_mode">
                            <?php _e('Multisite Network Mode', 'one-click-maintenance'); ?>
                        </label>
                        <label class="ocm-toggle-switch">
                            <input type="checkbox" id="ocm_multisite_mode" name="ocm_multisite_mode" value="1" <?php checked($multisite_mode); ?>>
                            <span class="ocm-toggle-slider"></span>
                        </label>
                        <p class="ocm-field-description"><?php _e('Apply maintenance mode across the entire multisite network.', 'one-click-maintenance'); ?></p>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="ocm-section">
                    <h2 class="ocm-section-title"><?php _e('Import/Export Settings', 'one-click-maintenance'); ?></h2>

                    <div class="ocm-field-group">
                        <label><?php _e('Export Settings', 'one-click-maintenance'); ?></label>
                        <button type="button" class="button button-secondary" id="ocm-export-settings">
                            <span class="dashicons dashicons-download"></span>
                            <?php _e('Export Settings', 'one-click-maintenance'); ?>
                        </button>
                        <p class="ocm-field-description"><?php _e('Download your current plugin settings as a JSON file.', 'one-click-maintenance'); ?></p>
                    </div>

                    <div class="ocm-field-group">
                        <label for="ocm-import-file"><?php _e('Import Settings', 'one-click-maintenance'); ?></label>
                        <input type="file" id="ocm-import-file" accept=".json">
                        <button type="button" class="button button-secondary" id="ocm-import-settings">
                            <span class="dashicons dashicons-upload"></span>
                            <?php _e('Import Settings', 'one-click-maintenance'); ?>
                        </button>
                        <p class="ocm-field-description"><?php _e('Upload a previously exported settings file to restore configuration.', 'one-click-maintenance'); ?></p>
                    </div>
                </div>

                <div class="ocm-section">
                    <h2 class="ocm-section-title"><?php _e('Developer Hooks', 'one-click-maintenance'); ?></h2>

                    <div class="ocm-field-group">
                        <label><?php _e('Available Filters & Actions', 'one-click-maintenance'); ?></label>
                        <div class="ocm-developer-hooks">
                            <code>ocm_maintenance_page_content</code> - Filter maintenance page content<br>
                            <code>ocm_bypass_maintenance</code> - Filter user bypass capability<br>
                            <code>ocm_countdown_labels</code> - Filter countdown timer labels<br>
                            <code>ocm_social_links</code> - Filter social media links<br>
                            <code>ocm_before_maintenance_page</code> - Action before maintenance page loads<br>
                            <code>ocm_after_maintenance_page</code> - Action after maintenance page loads
                        </div>
                        <p class="ocm-field-description"><?php _e('Use these hooks in your theme or custom plugins to extend functionality.', 'one-click-maintenance'); ?></p>
                    </div>
                </div>

                <?php endif; ?>
                
                <button type="submit" name="submit" class="ocm-save-button">
                    <?php _e('Save Settings', 'one-click-maintenance'); ?>
                </button>
            </form>
        </div>
        
        <div class="ocm-preview-panel">
            <h3><?php _e('Live Preview', 'one-click-maintenance'); ?></h3>
            <div class="ocm-preview-frame" id="ocm-preview">
                <p><?php _e('Preview will appear here when maintenance mode is configured.', 'one-click-maintenance'); ?></p>
            </div>
            <p style="margin-top: 15px; font-size: 13px; color: #666;">
                <?php _e('This preview shows how your maintenance page will look to visitors.', 'one-click-maintenance'); ?>
            </p>
        </div>
    </div>
</div>
