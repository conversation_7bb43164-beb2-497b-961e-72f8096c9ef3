=== One-Click Maintenance Mode ===
Contributors: yourname
Tags: maintenance, maintenance mode, coming soon, under construction, offline
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Easily activate a maintenance mode screen with one click, customizable content, and modern clean design.

== Description ==

**One-Click Maintenance Mode** is a powerful yet simple WordPress plugin that allows you to quickly put your website into maintenance mode with just one click. Perfect for when you need to perform updates, maintenance, or make changes to your site without visitors seeing a broken or incomplete website.

### ✨ Key Features

* **One-Click Activation** - Toggle maintenance mode on/off instantly from your WordPress admin
* **Admin Access** - Logged-in administrators can still access the site while maintenance mode is active
* **Customizable Design** - Full control over the maintenance page appearance
* **Modern Clean Design** - Beautiful, responsive maintenance page that looks great on all devices
* **Countdown Timer** - Optional countdown timer to show when maintenance will be completed
* **SEO Friendly** - Sends proper 503 Service Unavailable headers to search engines
* **Translation Ready** - Fully translatable with included .pot file

### 🎨 Customization Options

* **Content Customization**
  - Custom title and description text
  - Footer message with contact information
  - Logo upload support

* **Design Options**
  - Multiple Google Fonts (DM Sans, Inter, Roboto, Open Sans, Lato, Poppins)
  - Font size options (Small, Medium, Large)
  - Background color picker or custom background image
  - Custom CSS for advanced styling

* **Countdown Timer**
  - Enable/disable countdown functionality
  - Set specific end date and time
  - Automatic completion message when countdown expires

### 🚀 Easy to Use

1. Install and activate the plugin
2. Go to Settings > Maintenance Mode
3. Customize your maintenance page
4. Toggle maintenance mode on with one click
5. Your visitors see the maintenance page while you can still access the admin area

### 🔧 Technical Features

* Compatible with all WordPress versions 5.0+
* Clean, well-documented code
* No database bloat - uses WordPress options API
* Responsive design works on all devices
* Fast loading with optimized CSS and JavaScript
* Accessibility features for screen readers

### 🌐 Perfect For

* Website maintenance and updates
* Plugin/theme testing
* Content updates
* Server maintenance
* Coming soon pages
* Temporary site closure

== Installation ==

### Automatic Installation

1. Log in to your WordPress admin panel
2. Go to Plugins > Add New
3. Search for "One-Click Maintenance Mode"
4. Click "Install Now" and then "Activate"

### Manual Installation

1. Download the plugin zip file
2. Log in to your WordPress admin panel
3. Go to Plugins > Add New > Upload Plugin
4. Choose the zip file and click "Install Now"
5. Activate the plugin

### After Installation

1. Go to Settings > Maintenance Mode in your WordPress admin
2. Customize your maintenance page settings
3. Enable maintenance mode when ready

== Frequently Asked Questions ==

= Can I still access my website when maintenance mode is enabled? =

Yes! Logged-in administrators can still access the website normally. Only non-logged-in visitors will see the maintenance page.

= Will search engines be affected by maintenance mode? =

The plugin sends proper 503 Service Unavailable headers to search engines, indicating that the maintenance is temporary and they should check back later.

= Can I customize the maintenance page design? =

Absolutely! You can customize the title, description, colors, fonts, background, logo, and even add custom CSS for complete control over the appearance.

= Does the countdown timer work in real-time? =

Yes, the countdown timer updates every second and shows days, hours, minutes, and seconds remaining until the specified end time.

= Is the plugin translation ready? =

Yes, the plugin is fully translation ready and includes a .pot file for translators.

= Will this work with my theme? =

Yes, the maintenance page is completely independent of your theme and will work with any WordPress theme.

= Can I preview the maintenance page before enabling it? =

Yes, the admin settings page includes a live preview that shows exactly how your maintenance page will look to visitors.

== Screenshots ==

1. Admin settings page with live preview
2. Maintenance page with countdown timer
3. Mobile responsive design
4. Color and font customization options
5. Background image upload interface

== Changelog ==

= 1.0.0 =
* Initial release
* One-click maintenance mode activation
* Customizable maintenance page design
* Countdown timer functionality
* Admin live preview
* SEO-friendly 503 headers
* Translation ready
* Responsive design
* Custom CSS support
* Logo and background image upload
* Multiple Google Fonts support

== Upgrade Notice ==

= 1.0.0 =
Initial release of One-Click Maintenance Mode plugin.

== Support ==

For support, feature requests, or bug reports, please visit our support forum or contact us through our website.

== Credits ==

* Font: DM Sans by Google Fonts
* Icons: WordPress Dashicons
* Inspiration: Modern web design principles

== License ==

This plugin is licensed under the GPLv2 or later license.
